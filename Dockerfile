FROM python:3.12-alpine3.21

# Define build arguments that can be passed from docker-compose
ARG DB_HOST
ARG DB_PORT
ARG DB_USER
ARG DB_PASSWORD
ARG DB_NAME
ARG DATA_DIR=/app/data
ARG ADDONS_PATH=/app/odoo/addons,/app/addons,/app/custom-addons
# ARG ADDONS_PATH=/app/addons,/app/custom-addons

# Set environment variables from build arguments
ENV HOST=$DB_HOST \
    PORT=$DB_PORT \
    USER=$DB_USER \
    PASSWORD=$DB_PASSWORD \
    DATABASE=$DB_NAME \
    DATA_DIR=$DATA_DIR \
    ADDONS_PATH=$ADDONS_PATH \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apk add --no-cache \
    git \
    build-base \
    libxml2-dev \
    libxslt-dev \
    openldap-dev \
    libjpeg-turbo-dev \
    zlib-dev \
    postgresql-dev \
    freetype-dev \
    lcms2-dev \
    libwebp-dev \
    harfbuzz-dev \
    fribidi-dev \
    postgresql-client \
    npm \
    nodejs \
    python3-dev \
    cython

# Create app directory
RUN mkdir -p /app
WORKDIR /app

# Clone Odoo repository
RUN git clone https://github.com/odoo/odoo.git .

# Install Python dependencies in the correct order
RUN pip install --no-cache-dir --upgrade pip setuptools wheel

# Install remaining dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Set up Odoo configuration
RUN mkdir -p /app/data/filestore
# RUN mkdir -p /app/custom-addons
# RUN chown -R 1000:1000 /app/custom-addons

# Copy entrypoint script
COPY entrypoint.sh /app/entrypoint.sh
RUN chmod +x /app/entrypoint.sh

# Expose Odoo port
EXPOSE 8069 8071 8072

# Set the entrypoint
ENTRYPOINT ["/app/entrypoint.sh"]
